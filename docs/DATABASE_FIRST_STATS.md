# Database-First Stats Architecture

## Overview

The SanityAI application has been enhanced with a **database-first stats architecture** that provides better performance, reliability, and consistency compared to the previous Redis-dependent approach.

## Key Improvements

### 1. **Database as Primary Source of Truth**
- All statistics are primarily stored and retrieved from the PostgreSQL database
- Redis is used only as a temporary buffer for real-time data collection
- Eliminates data loss risks and provides better consistency

### 2. **Enhanced Performance**
- Optimized database queries with proper indexing
- Efficient aggregation using SQL functions (SUM, AVG, COUNT)
- Reduced dependency on Redis for critical operations

### 3. **Better Reliability**
- Graceful fallback mechanisms when Redis is unavailable
- Comprehensive error handling and logging
- Transactional data integrity

### 4. **Improved API Endpoints**
- New `/stats/database` endpoint for comprehensive statistics
- New `/stats/timeseries` endpoint for analytics data
- Enhanced existing endpoints with database-first approach

## New API Endpoints

### GET /stats/database
**Recommended endpoint for all stats queries**

```bash
# Get today's statistics
GET /stats/database?timeRange=today

# Get last 7 days statistics
GET /stats/database?timeRange=7d

# Get last 30 days statistics
GET /stats/database?timeRange=30d
```

**Response Format:**
```json
{
  "success": true,
  "timeRange": "today",
  "startDate": "2024-01-15",
  "endDate": "2024-01-15",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "dataSource": "database",
  "stats": {
    "requests": {
      "totalRequests": 1250,
      "filteredRequests": 1100,
      "blockedRequests": 150,
      "cachedRequests": 800,
      "cacheHitRate": 64,
      "avgResponseTime": 245,
      "p95ResponseTime": 450,
      "daysWithData": 1
    },
    "api": {
      "text": {
        "calls": 950,
        "errors": 12,
        "errorRate": 1,
        "avgResponseTime": 180,
        "hoursWithData": 8
      },
      "image": {
        "calls": 300,
        "errors": 5,
        "errorRate": 2,
        "avgResponseTime": 420,
        "hoursWithData": 6
      }
    },
    "flags": {
      "flags": {
        "inappropriate": { "count": 45, "daysActive": 1 },
        "spam": { "count": 23, "daysActive": 1 }
      },
      "totalFlags": 68,
      "uniqueFlags": 2
    },
    "users": {
      "totalUsers": 125,
      "totalRequests": 1250,
      "avgRequestsPerUser": 10,
      "maxRequestsPerUser": 45
    }
  }
}
```

### GET /stats/timeseries
**For charts and analytics dashboards**

```bash
# Get daily time-series data
GET /stats/timeseries?startDate=2024-01-01&endDate=2024-01-07&granularity=daily

# Get hourly time-series data
GET /stats/timeseries?startDate=2024-01-15&endDate=2024-01-15&granularity=hourly
```

**Response Format:**
```json
{
  "success": true,
  "startDate": "2024-01-01",
  "endDate": "2024-01-07",
  "granularity": "daily",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "data": [
    {
      "date": "2024-01-01",
      "totalRequests": 1200,
      "filteredRequests": 1050,
      "blockedRequests": 150,
      "cachedRequests": 750,
      "avgResponseTime": 230,
      "p95ResponseTime": 420
    }
  ]
}
```

## Enhanced Aggregation System

### New Enhanced Aggregator
The new aggregation system provides:
- **Parallel Processing**: All aggregation tasks run concurrently
- **Better Error Handling**: Individual task failures don't stop the entire process
- **Comprehensive Reporting**: Detailed success/failure reporting
- **Performance Monitoring**: Execution time tracking

### Running Enhanced Aggregation

```bash
# Run enhanced aggregation with full reporting
bun run stats:aggregate:enhanced

# Run in quiet mode (less output)
bun run stats:aggregate:enhanced --quiet

# Run in verbose mode (debug logging)
bun run stats:aggregate:enhanced --verbose

# Show help
bun run stats:aggregate:enhanced --help
```

### Aggregation Output Example
```
🚀 Enhanced Stats Aggregator Starting...

📊 Running enhanced stats aggregation...

📋 AGGREGATION RESULTS:
   ⏱️  Timestamp: 2024-01-15T10:30:00.000Z
   ✅ Overall Success: YES

   📈 Individual Task Results:
      ✅ requestStats: SUCCESS
      ✅ apiPerformance: SUCCESS
      ✅ contentFlags: SUCCESS
      ✅ userActivity: SUCCESS

📊 CURRENT DATABASE STATS:
   📅 Today (2024-01-15):
      Total Requests: 1250
      Blocked Requests: 150
      Cache Hit Rate: 64%
      Avg Response Time: 245ms

⚡ PERFORMANCE SUMMARY:
   Duration: 1250ms
   Status: SUCCESS

💡 RECOMMENDATIONS:
   ✅ All aggregation tasks completed successfully
   🔄 Consider running this aggregation hourly for best results
   📊 Use the new /stats/database endpoint for better performance
```

## Database Schema

### Tables Used
1. **request_stats_daily** - Daily aggregated request statistics
2. **api_performance_hourly** - Hourly API performance metrics
3. **content_flags_daily** - Daily content flag statistics
4. **user_activity_daily** - Daily user activity statistics

### Key Features
- **Proper Indexing**: Optimized for fast queries
- **Data Accumulation**: New data is added to existing records
- **Transactional Integrity**: All operations are wrapped in transactions
- **Automatic Timestamps**: Created/updated timestamps for audit trails

## Migration Guide

### For API Consumers
1. **Switch to new endpoints**: Use `/stats/database` instead of `/stats/summary`
2. **Update time range parameters**: Use `timeRange` instead of custom date ranges
3. **Handle new response format**: The response structure has been improved

### For Developers
1. **Use database-first services**: Import from `statsDbFirstService.ts`
2. **Run enhanced aggregation**: Use the new aggregation script
3. **Monitor performance**: Check aggregation logs and database performance

## Performance Benefits

### Before (Redis-dependent)
- ❌ Single point of failure (Redis)
- ❌ Data loss risk during Redis failures
- ❌ Complex dual-system maintenance
- ❌ Inconsistent data sources across endpoints

### After (Database-first)
- ✅ Reliable database as primary source
- ✅ Graceful Redis fallback
- ✅ Consistent data across all endpoints
- ✅ Better performance with optimized queries
- ✅ Comprehensive error handling

## Monitoring and Maintenance

### Regular Tasks
1. **Run aggregation hourly**: `bun run stats:aggregate:enhanced`
2. **Monitor database performance**: Check query execution times
3. **Verify data consistency**: Compare database vs Redis data
4. **Clean up old data**: Archive or delete old statistics as needed

### Troubleshooting
1. **Check database connection**: Ensure PostgreSQL is accessible
2. **Verify Redis status**: Redis should be available for real-time data
3. **Review aggregation logs**: Check for any failed tasks
4. **Monitor disk space**: Database growth with historical data

## Future Enhancements

### Planned Features
1. **Real-time dashboards**: WebSocket-based live statistics
2. **Advanced analytics**: Machine learning insights
3. **Data export**: CSV/JSON export functionality
4. **Automated reporting**: Scheduled email reports

### Performance Optimizations
1. **Query optimization**: Further database query improvements
2. **Caching layer**: Intelligent caching for frequently accessed data
3. **Data partitioning**: Partition large tables by date
4. **Archival system**: Automated old data archival

## Conclusion

The database-first stats architecture provides a robust, reliable, and performant solution for statistics management in SanityAI. It eliminates the previous Redis dependency issues while providing better data consistency and improved API performance.

For any questions or issues, please refer to the application logs or contact the development team.
