{"name": "filterx", "module": "src/server.ts", "main": "src/server.ts", "type": "module", "scripts": {"start": "bun run src/server.ts", "dev": "bun run --watch src/server.ts", "build": "bun build src/server.ts --target node --outdir dist", "prod": "NODE_ENV=production bun run dist/server.js", "test": "bun test", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "lint:report": "eslint src --ext .ts --output-file eslint-report.json --format json", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "analyze": "bun build src/server.ts --analyze", "clean": "rm -rf dist", "docker:build": "docker build -t filterx:latest .", "prepare-db": "bun run scripts/prepare-db.ts", "test-redis": "bun run scripts/test-redis.ts", "stats:aggregate": "bun run src/workers/statsAggregator.ts", "stats:aggregate:display": "node scripts/stats-aggregator.js", "stats:check-db": "node scripts/check-db-stats.js", "stats:diagnose": "node scripts/diagnose-stats.js", "test:model-tiers": "bun run test-model-tiers.js", "test:providers": "node test-provider-integration.js", "check:ts": "tsc --noEmit"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bun": "latest", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/morgan": "^1.9.9", "@types/pg": "^8.11.13", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"@google/genai": "0.3.1", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.10", "axios": "^1.8.4", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mime": "4.0.7", "moondream": "^0.1.0", "morgan": "^1.10.0", "pg": "^8.14.1", "redis": "^4.7.0", "uuid": "^11.1.0"}}